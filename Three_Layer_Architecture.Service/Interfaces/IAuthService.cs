using Three_Layer_Architecture.Repository.Models.DTOs;

namespace Three_Layer_Architecture.Service.Interfaces;

public interface IAuthService
{
    Task<AuthResponse?> LoginAsync(LoginRequest request);
    Task<AuthResponse> RegisterAsync(RegisterRequest request);
    Task<string> GenerateJwtTokenAsync(int userId, string username, string role);
    Task<bool> ValidateTokenAsync(string token);
    Task<UserDto?> GetCurrentUserAsync(int userId);
}
