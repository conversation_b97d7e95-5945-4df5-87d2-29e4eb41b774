using Three_Layer_Architecture.Repository.Interfaces;
using Three_Layer_Architecture.Repository.Models;
using Three_Layer_Architecture.Service.Interfaces;

namespace Three_Layer_Architecture.Service.Services;

public class WeatherService : IWeatherService
{
    private readonly IWeatherRepository _weatherRepository;

    public WeatherService(IWeatherRepository weatherRepository)
    {
        _weatherRepository = weatherRepository ?? throw new ArgumentNullException(nameof(weatherRepository));
    }

    public async Task<IEnumerable<WeatherForecast>> GetWeatherForecastsAsync(int count = 5)
    {
        // 業務邏輯驗證
        if (count <= 0)
        {
            throw new ArgumentException("Count must be greater than zero.", nameof(count));
        }

        if (count > 30)
        {
            throw new ArgumentException("Count cannot exceed 30 days.", nameof(count));
        }

        return await _weatherRepository.GetWeatherForecastsAsync(count);
    }

    public async Task<WeatherForecast?> GetWeatherForecastByDateAsync(DateOnly date)
    {
        // 業務邏輯驗證
        if (date < DateOnly.FromDateTime(DateTime.Now.Date))
        {
            throw new ArgumentException("Cannot retrieve weather forecast for past dates.", nameof(date));
        }

        return await _weatherRepository.GetWeatherForecastByDateAsync(date);
    }

    public async Task<WeatherForecast> CreateWeatherForecastAsync(WeatherForecast weatherForecast)
    {
        // 業務邏輯驗證
        if (weatherForecast == null)
        {
            throw new ArgumentNullException(nameof(weatherForecast));
        }

        if (weatherForecast.Date < DateOnly.FromDateTime(DateTime.Now.Date))
        {
            throw new ArgumentException("Cannot create weather forecast for past dates.");
        }

        if (!await IsValidTemperatureAsync(weatherForecast.TemperatureC))
        {
            throw new ArgumentException("Temperature is outside valid range (-50°C to 60°C).");
        }

        if (string.IsNullOrWhiteSpace(weatherForecast.Summary))
        {
            throw new ArgumentException("Weather summary cannot be empty.");
        }

        return await _weatherRepository.CreateWeatherForecastAsync(weatherForecast);
    }

    public async Task<WeatherForecast?> UpdateWeatherForecastAsync(WeatherForecast weatherForecast)
    {
        // 業務邏輯驗證
        if (weatherForecast == null)
        {
            throw new ArgumentNullException(nameof(weatherForecast));
        }

        if (!await IsValidTemperatureAsync(weatherForecast.TemperatureC))
        {
            throw new ArgumentException("Temperature is outside valid range (-50°C to 60°C).");
        }

        if (string.IsNullOrWhiteSpace(weatherForecast.Summary))
        {
            throw new ArgumentException("Weather summary cannot be empty.");
        }

        return await _weatherRepository.UpdateWeatherForecastAsync(weatherForecast);
    }

    public async Task<bool> DeleteWeatherForecastAsync(DateOnly date)
    {
        // 業務邏輯驗證
        if (date < DateOnly.FromDateTime(DateTime.Now.Date))
        {
            throw new ArgumentException("Cannot delete weather forecast for past dates.", nameof(date));
        }

        return await _weatherRepository.DeleteWeatherForecastAsync(date);
    }

    public async Task<bool> IsValidTemperatureAsync(int temperatureC)
    {
        await Task.CompletedTask; // 模擬非同步業務邏輯
        
        // 業務規則：溫度必須在合理範圍內
        return temperatureC >= -50 && temperatureC <= 60;
    }
}
