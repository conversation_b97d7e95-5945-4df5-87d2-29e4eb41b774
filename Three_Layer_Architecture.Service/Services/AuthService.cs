using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Three_Layer_Architecture.Repository.Interfaces;
using Three_Layer_Architecture.Repository.Models;
using Three_Layer_Architecture.Repository.Models.DTOs;
using Three_Layer_Architecture.Service.Interfaces;

namespace Three_Layer_Architecture.Service.Services;

public class AuthService : IAuthService
{
    private readonly IUserRepository _userRepository;
    private readonly IConfiguration _configuration;

    public AuthService(IUserRepository userRepository, IConfiguration configuration)
    {
        _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    public async Task<AuthResponse?> LoginAsync(LoginRequest request)
    {
        // 業務邏輯驗證
        if (request == null)
        {
            throw new ArgumentNullException(nameof(request));
        }

        if (string.IsNullOrWhiteSpace(request.Username) || string.IsNullOrWhiteSpace(request.Password))
        {
            throw new ArgumentException("使用者名稱和密碼不能為空");
        }

        // 查找用戶
        var user = await _userRepository.GetUserByUsernameAsync(request.Username);
        if (user == null || !user.IsActive)
        {
            return null; // 用戶不存在或已停用
        }

        // 驗證密碼
        if (!BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
        {
            return null; // 密碼錯誤
        }

        // 更新最後登入時間
        await _userRepository.UpdateLastLoginAsync(user.Id);

        // 產生 JWT Token
        var token = await GenerateJwtTokenAsync(user.Id, user.Username, user.Role);
        var expiresAt = DateTime.UtcNow.AddHours(24); // Token 有效期 24 小時

        return new AuthResponse
        {
            Token = token,
            Username = user.Username,
            Email = user.Email,
            Role = user.Role,
            ExpiresAt = expiresAt
        };
    }

    public async Task<AuthResponse> RegisterAsync(RegisterRequest request)
    {
        // 業務邏輯驗證
        if (request == null)
        {
            throw new ArgumentNullException(nameof(request));
        }

        if (string.IsNullOrWhiteSpace(request.Username))
        {
            throw new ArgumentException("使用者名稱不能為空");
        }

        if (string.IsNullOrWhiteSpace(request.Email))
        {
            throw new ArgumentException("電子郵件不能為空");
        }

        if (string.IsNullOrWhiteSpace(request.Password))
        {
            throw new ArgumentException("密碼不能為空");
        }

        if (request.Password != request.ConfirmPassword)
        {
            throw new ArgumentException("密碼與確認密碼不符");
        }

        if (request.Password.Length < 6)
        {
            throw new ArgumentException("密碼長度至少需要6個字元");
        }

        // 檢查用戶是否已存在
        if (await _userRepository.UserExistsAsync(request.Username, request.Email))
        {
            throw new InvalidOperationException("使用者名稱或電子郵件已存在");
        }

        // 建立新用戶
        var user = new User
        {
            Username = request.Username,
            Email = request.Email,
            PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
            Role = "User", // 預設角色
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        };

        var createdUser = await _userRepository.CreateUserAsync(user);

        // 產生 JWT Token
        var token = await GenerateJwtTokenAsync(createdUser.Id, createdUser.Username, createdUser.Role);
        var expiresAt = DateTime.UtcNow.AddHours(24);

        return new AuthResponse
        {
            Token = token,
            Username = createdUser.Username,
            Email = createdUser.Email,
            Role = createdUser.Role,
            ExpiresAt = expiresAt
        };
    }

    public async Task<string> GenerateJwtTokenAsync(int userId, string username, string role)
    {
        await Task.CompletedTask; // 模擬非同步操作

        var jwtSettings = _configuration.GetSection("JwtSettings");
        var secretKey = jwtSettings["SecretKey"] ?? "YourSuperSecretKeyThatIsAtLeast32CharactersLong!";
        var issuer = jwtSettings["Issuer"] ?? "ThreeLayerArchitectureAPI";
        var audience = jwtSettings["Audience"] ?? "ThreeLayerArchitectureAPI";

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(ClaimTypes.NameIdentifier, userId.ToString()),
            new Claim(ClaimTypes.Name, username),
            new Claim(ClaimTypes.Role, role),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        var token = new JwtSecurityToken(
            issuer: issuer,
            audience: audience,
            claims: claims,
            expires: DateTime.UtcNow.AddHours(24),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public async Task<bool> ValidateTokenAsync(string token)
    {
        await Task.CompletedTask; // 模擬非同步操作

        try
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var secretKey = jwtSettings["SecretKey"] ?? "YourSuperSecretKeyThatIsAtLeast32CharactersLong!";
            var issuer = jwtSettings["Issuer"] ?? "ThreeLayerArchitectureAPI";
            var audience = jwtSettings["Audience"] ?? "ThreeLayerArchitectureAPI";

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));

            var tokenHandler = new JwtSecurityTokenHandler();
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = key,
                ValidateIssuer = true,
                ValidIssuer = issuer,
                ValidateAudience = true,
                ValidAudience = audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            };

            tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public async Task<UserDto?> GetCurrentUserAsync(int userId)
    {
        var user = await _userRepository.GetUserByIdAsync(userId);
        if (user == null)
        {
            return null;
        }

        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            Role = user.Role,
            CreatedAt = user.CreatedAt,
            LastLoginAt = user.LastLoginAt,
            IsActive = user.IsActive
        };
    }
}
