using Three_Layer_Architecture.Repository.Interfaces;
using Three_Layer_Architecture.Repository.Models;
using Three_Layer_Architecture.Repository.Models.DTOs;
using Three_Layer_Architecture.Service.Interfaces;

namespace Three_Layer_Architecture.Service.Services;

public class UserService : IUserService
{
    private readonly IUserRepository _userRepository;

    public UserService(IUserRepository userRepository)
    {
        _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
    }

    public async Task<UserDto?> GetUserByIdAsync(int id)
    {
        if (id <= 0)
        {
            throw new ArgumentException("用戶ID必須大於0", nameof(id));
        }

        var user = await _userRepository.GetUserByIdAsync(id);
        if (user == null)
        {
            return null;
        }

        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            Role = user.Role,
            CreatedAt = user.CreatedAt,
            LastLoginAt = user.LastLoginAt,
            IsActive = user.IsActive
        };
    }

    public async Task<UserDto?> GetUserByUsernameAsync(string username)
    {
        if (string.IsNullOrWhiteSpace(username))
        {
            throw new ArgumentException("使用者名稱不能為空", nameof(username));
        }

        var user = await _userRepository.GetUserByUsernameAsync(username);
        if (user == null)
        {
            return null;
        }

        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            Role = user.Role,
            CreatedAt = user.CreatedAt,
            LastLoginAt = user.LastLoginAt,
            IsActive = user.IsActive
        };
    }

    public async Task<IEnumerable<UserDto>> GetAllUsersAsync()
    {
        var users = await _userRepository.GetAllUsersAsync();
        
        return users.Select(user => new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            Role = user.Role,
            CreatedAt = user.CreatedAt,
            LastLoginAt = user.LastLoginAt,
            IsActive = user.IsActive
        });
    }

    public async Task<UserDto?> UpdateUserAsync(int id, UserDto userDto)
    {
        if (id <= 0)
        {
            throw new ArgumentException("用戶ID必須大於0", nameof(id));
        }

        if (userDto == null)
        {
            throw new ArgumentNullException(nameof(userDto));
        }

        if (string.IsNullOrWhiteSpace(userDto.Username))
        {
            throw new ArgumentException("使用者名稱不能為空");
        }

        if (string.IsNullOrWhiteSpace(userDto.Email))
        {
            throw new ArgumentException("電子郵件不能為空");
        }

        // 檢查用戶是否存在
        var existingUser = await _userRepository.GetUserByIdAsync(id);
        if (existingUser == null)
        {
            return null;
        }

        // 檢查使用者名稱或電子郵件是否被其他用戶使用
        var userWithSameUsername = await _userRepository.GetUserByUsernameAsync(userDto.Username);
        if (userWithSameUsername != null && userWithSameUsername.Id != id)
        {
            throw new InvalidOperationException("使用者名稱已被其他用戶使用");
        }

        var userWithSameEmail = await _userRepository.GetUserByEmailAsync(userDto.Email);
        if (userWithSameEmail != null && userWithSameEmail.Id != id)
        {
            throw new InvalidOperationException("電子郵件已被其他用戶使用");
        }

        // 更新用戶資訊
        existingUser.Username = userDto.Username;
        existingUser.Email = userDto.Email;
        existingUser.Role = userDto.Role;
        existingUser.IsActive = userDto.IsActive;

        var updatedUser = await _userRepository.UpdateUserAsync(existingUser);
        if (updatedUser == null)
        {
            return null;
        }

        return new UserDto
        {
            Id = updatedUser.Id,
            Username = updatedUser.Username,
            Email = updatedUser.Email,
            Role = updatedUser.Role,
            CreatedAt = updatedUser.CreatedAt,
            LastLoginAt = updatedUser.LastLoginAt,
            IsActive = updatedUser.IsActive
        };
    }

    public async Task<bool> DeleteUserAsync(int id)
    {
        if (id <= 0)
        {
            throw new ArgumentException("用戶ID必須大於0", nameof(id));
        }

        return await _userRepository.DeleteUserAsync(id);
    }

    public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
    {
        if (userId <= 0)
        {
            throw new ArgumentException("用戶ID必須大於0", nameof(userId));
        }

        if (string.IsNullOrWhiteSpace(currentPassword))
        {
            throw new ArgumentException("目前密碼不能為空", nameof(currentPassword));
        }

        if (string.IsNullOrWhiteSpace(newPassword))
        {
            throw new ArgumentException("新密碼不能為空", nameof(newPassword));
        }

        if (newPassword.Length < 6)
        {
            throw new ArgumentException("新密碼長度至少需要6個字元", nameof(newPassword));
        }

        // 取得用戶
        var user = await _userRepository.GetUserByIdAsync(userId);
        if (user == null)
        {
            return false;
        }

        // 驗證目前密碼
        if (!BCrypt.Net.BCrypt.Verify(currentPassword, user.PasswordHash))
        {
            throw new UnauthorizedAccessException("目前密碼不正確");
        }

        // 更新密碼
        user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
        var updatedUser = await _userRepository.UpdateUserAsync(user);

        return updatedUser != null;
    }
}
