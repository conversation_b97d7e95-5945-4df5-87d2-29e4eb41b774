using System.ComponentModel.DataAnnotations;

namespace Three_Layer_Architecture.Repository.Models.DTOs;

public class RegisterRequest
{
    [Required(ErrorMessage = "使用者名稱為必填")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "使用者名稱長度必須在3-50字元之間")]
    public string Username { get; set; } = string.Empty;

    [Required(ErrorMessage = "電子郵件為必填")]
    [EmailAddress(ErrorMessage = "請輸入有效的電子郵件格式")]
    public string Email { get; set; } = string.Empty;

    [Required(ErrorMessage = "密碼為必填")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "密碼長度必須在6-100字元之間")]
    public string Password { get; set; } = string.Empty;

    [Required(ErrorMessage = "確認密碼為必填")]
    [Compare("Password", ErrorMessage = "密碼與確認密碼不符")]
    public string ConfirmPassword { get; set; } = string.Empty;
}
