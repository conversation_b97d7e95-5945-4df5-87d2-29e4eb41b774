using Three_Layer_Architecture.Repository.Interfaces;
using Three_Layer_Architecture.Repository.Models;

namespace Three_Layer_Architecture.Repository.Repositories;

public class UserRepository : IUserRepository
{
    // 模擬資料庫儲存
    private static readonly List<User> _users = new();
    private static int _nextId = 1;

    static UserRepository()
    {
        // 初始化一些測試用戶
        _users.Add(new User
        {
            Id = _nextId++,
            Username = "admin",
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
            Role = "Admin",
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        });

        _users.Add(new User
        {
            Id = _nextId++,
            Username = "user",
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("user123"),
            Role = "User",
            CreatedAt = DateTime.UtcNow,
            IsActive = true
        });
    }

    public async Task<User?> GetUserByIdAsync(int id)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        return _users.FirstOrDefault(u => u.Id == id && u.IsActive);
    }

    public async Task<User?> GetUserByUsernameAsync(string username)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        return _users.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) && u.IsActive);
    }

    public async Task<User?> GetUserByEmailAsync(string email)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        return _users.FirstOrDefault(u => u.Email.Equals(email, StringComparison.OrdinalIgnoreCase) && u.IsActive);
    }

    public async Task<User> CreateUserAsync(User user)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        
        user.Id = _nextId++;
        user.CreatedAt = DateTime.UtcNow;
        _users.Add(user);
        
        return user;
    }

    public async Task<User?> UpdateUserAsync(User user)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        
        var existingUser = _users.FirstOrDefault(u => u.Id == user.Id);
        if (existingUser == null)
        {
            return null;
        }

        existingUser.Username = user.Username;
        existingUser.Email = user.Email;
        existingUser.Role = user.Role;
        existingUser.IsActive = user.IsActive;
        
        return existingUser;
    }

    public async Task<bool> DeleteUserAsync(int id)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        
        var user = _users.FirstOrDefault(u => u.Id == id);
        if (user == null)
        {
            return false;
        }

        // 軟刪除
        user.IsActive = false;
        return true;
    }

    public async Task<IEnumerable<User>> GetAllUsersAsync()
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        return _users.Where(u => u.IsActive).ToList();
    }

    public async Task<bool> UserExistsAsync(string username, string email)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        return _users.Any(u => u.IsActive && 
            (u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) || 
             u.Email.Equals(email, StringComparison.OrdinalIgnoreCase)));
    }

    public async Task<bool> UpdateLastLoginAsync(int userId)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        
        var user = _users.FirstOrDefault(u => u.Id == userId && u.IsActive);
        if (user == null)
        {
            return false;
        }

        user.LastLoginAt = DateTime.UtcNow;
        return true;
    }
}
