using Three_Layer_Architecture.Repository.Interfaces;
using Three_Layer_Architecture.Repository.Models;

namespace Three_Layer_Architecture.Repository.Repositories;

public class WeatherRepository : IWeatherRepository
{
    private static readonly string[] Summaries = new[]
    {
        "Freezing", "Bracing", "Chi<PERSON>", "Cool", "Mild", "Warm", "<PERSON><PERSON><PERSON>", "Hot", "Sweltering", "Scorching"
    };

    // 模擬資料庫儲存
    private static readonly List<WeatherForecast> _weatherForecasts = new();

    public async Task<IEnumerable<WeatherForecast>> GetWeatherForecastsAsync(int count = 5)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作

        // 如果沒有資料，產生一些範例資料
        if (!_weatherForecasts.Any())
        {
            var forecasts = Enumerable.Range(1, count).Select(index => new WeatherForecast
            {
                Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
                TemperatureC = Random.Shared.Next(-20, 55),
                Summary = Summaries[Random.Shared.Next(Summaries.Length)]
            }).ToList();

            _weatherForecasts.AddRange(forecasts);
        }

        return _weatherForecasts.Take(count);
    }

    public async Task<WeatherForecast?> GetWeatherForecastByDateAsync(DateOnly date)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        return _weatherForecasts.FirstOrDefault(w => w.Date == date);
    }

    public async Task<WeatherForecast> CreateWeatherForecastAsync(WeatherForecast weatherForecast)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        
        // 檢查是否已存在相同日期的預報
        var existing = _weatherForecasts.FirstOrDefault(w => w.Date == weatherForecast.Date);
        if (existing != null)
        {
            throw new InvalidOperationException($"Weather forecast for date {weatherForecast.Date} already exists.");
        }

        _weatherForecasts.Add(weatherForecast);
        return weatherForecast;
    }

    public async Task<WeatherForecast?> UpdateWeatherForecastAsync(WeatherForecast weatherForecast)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        
        var existing = _weatherForecasts.FirstOrDefault(w => w.Date == weatherForecast.Date);
        if (existing == null)
        {
            return null;
        }

        existing.TemperatureC = weatherForecast.TemperatureC;
        existing.Summary = weatherForecast.Summary;
        
        return existing;
    }

    public async Task<bool> DeleteWeatherForecastAsync(DateOnly date)
    {
        await Task.Delay(10); // 模擬非同步資料庫操作
        
        var existing = _weatherForecasts.FirstOrDefault(w => w.Date == date);
        if (existing == null)
        {
            return false;
        }

        _weatherForecasts.Remove(existing);
        return true;
    }
}
