using Three_Layer_Architecture.Repository.Models;

namespace Three_Layer_Architecture.Repository.Interfaces;

public interface IWeatherRepository
{
    Task<IEnumerable<WeatherForecast>> GetWeatherForecastsAsync(int count = 5);
    Task<WeatherForecast?> GetWeatherForecastByDateAsync(DateOnly date);
    Task<WeatherForecast> CreateWeatherForecastAsync(WeatherForecast weatherForecast);
    Task<WeatherForecast?> UpdateWeatherForecastAsync(WeatherForecast weatherForecast);
    Task<bool> DeleteWeatherForecastAsync(DateOnly date);
}
