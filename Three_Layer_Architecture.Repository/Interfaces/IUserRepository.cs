using Three_Layer_Architecture.Repository.Models;

namespace Three_Layer_Architecture.Repository.Interfaces;

public interface IUserRepository
{
    Task<User?> GetUserByIdAsync(int id);
    Task<User?> GetUserByUsernameAsync(string username);
    Task<User?> GetUserByEmailAsync(string email);
    Task<User> CreateUserAsync(User user);
    Task<User?> UpdateUserAsync(User user);
    Task<bool> DeleteUserAsync(int id);
    Task<IEnumerable<User>> GetAllUsersAsync();
    Task<bool> UserExistsAsync(string username, string email);
    Task<bool> UpdateLastLoginAsync(int userId);
}
