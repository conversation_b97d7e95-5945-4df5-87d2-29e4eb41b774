using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Three_Layer_Architecture.Repository.Models.DTOs;
using Three_Layer_Architecture.Service.Interfaces;

namespace Three_Layer_Architecture.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UsersController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly ILogger<UsersController> _logger;

    public UsersController(IUserService userService, ILogger<UsersController> logger)
    {
        _userService = userService ?? throw new ArgumentNullException(nameof(userService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 取得所有用戶（僅管理員）
    /// </summary>
    /// <returns>用戶清單</returns>
    [HttpGet]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult<IEnumerable<UserDto>>> GetAllUsers()
    {
        try
        {
            _logger.LogInformation("Getting all users");
            
            var users = await _userService.GetAllUsersAsync();
            return Ok(users);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting all users");
            return StatusCode(500, new { message = "取得用戶清單時發生錯誤" });
        }
    }

    /// <summary>
    /// 根據ID取得用戶
    /// </summary>
    /// <param name="id">用戶ID</param>
    /// <returns>用戶資訊</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<UserDto>> GetUserById(int id)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var currentUserRole = User.FindFirst(ClaimTypes.Role)?.Value;

            // 只有管理員或用戶本人可以查看用戶資訊
            if (currentUserRole != "Admin" && currentUserId != id)
            {
                return Forbid();
            }

            _logger.LogInformation("Getting user by ID: {Id}", id);
            
            var user = await _userService.GetUserByIdAsync(id);
            if (user == null)
            {
                return NotFound(new { message = "用戶不存在" });
            }

            return Ok(user);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid argument: {Message}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting user by ID: {Id}", id);
            return StatusCode(500, new { message = "取得用戶資訊時發生錯誤" });
        }
    }

    /// <summary>
    /// 更新用戶資訊
    /// </summary>
    /// <param name="id">用戶ID</param>
    /// <param name="userDto">用戶資訊</param>
    /// <returns>更新後的用戶資訊</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<UserDto>> UpdateUser(int id, [FromBody] UserDto userDto)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            var currentUserRole = User.FindFirst(ClaimTypes.Role)?.Value;

            // 只有管理員或用戶本人可以更新用戶資訊
            if (currentUserRole != "Admin" && currentUserId != id)
            {
                return Forbid();
            }

            // 非管理員不能修改角色
            if (currentUserRole != "Admin")
            {
                var existingUser = await _userService.GetUserByIdAsync(id);
                if (existingUser != null)
                {
                    userDto.Role = existingUser.Role; // 保持原有角色
                }
            }

            _logger.LogInformation("Updating user: {Id}", id);
            
            var updatedUser = await _userService.UpdateUserAsync(id, userDto);
            if (updatedUser == null)
            {
                return NotFound(new { message = "用戶不存在" });
            }

            return Ok(updatedUser);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Update user validation error: {Message}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Update user conflict: {Message}", ex.Message);
            return Conflict(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating user: {Id}", id);
            return StatusCode(500, new { message = "更新用戶資訊時發生錯誤" });
        }
    }

    /// <summary>
    /// 刪除用戶（僅管理員）
    /// </summary>
    /// <param name="id">用戶ID</param>
    /// <returns>刪除結果</returns>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Admin")]
    public async Task<ActionResult> DeleteUser(int id)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            
            // 不能刪除自己
            if (currentUserId == id)
            {
                return BadRequest(new { message = "不能刪除自己的帳戶" });
            }

            _logger.LogInformation("Deleting user: {Id}", id);
            
            var result = await _userService.DeleteUserAsync(id);
            if (!result)
            {
                return NotFound(new { message = "用戶不存在" });
            }

            return Ok(new { message = "用戶已成功刪除" });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Delete user validation error: {Message}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting user: {Id}", id);
            return StatusCode(500, new { message = "刪除用戶時發生錯誤" });
        }
    }

    /// <summary>
    /// 變更密碼
    /// </summary>
    /// <param name="request">變更密碼請求</param>
    /// <returns>變更結果</returns>
    [HttpPost("change-password")]
    public async Task<ActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
    {
        try
        {
            var currentUserId = GetCurrentUserId();
            
            _logger.LogInformation("Changing password for user: {UserId}", currentUserId);
            
            var result = await _userService.ChangePasswordAsync(currentUserId, request.CurrentPassword, request.NewPassword);
            if (!result)
            {
                return BadRequest(new { message = "變更密碼失敗" });
            }

            return Ok(new { message = "密碼已成功變更" });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Change password validation error: {Message}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning("Change password unauthorized: {Message}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while changing password");
            return StatusCode(500, new { message = "變更密碼時發生錯誤" });
        }
    }

    private int GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
        {
            throw new UnauthorizedAccessException("無效的用戶身份");
        }
        return userId;
    }
}

public class ChangePasswordRequest
{
    public string CurrentPassword { get; set; } = string.Empty;
    public string NewPassword { get; set; } = string.Empty;
}
