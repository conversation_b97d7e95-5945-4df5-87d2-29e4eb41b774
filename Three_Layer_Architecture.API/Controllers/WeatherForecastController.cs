using Microsoft.AspNetCore.Mvc;
using Three_Layer_Architecture.Repository.Models;
using Three_Layer_Architecture.Service.Interfaces;

namespace Three_Layer_Architecture.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class WeatherForecastController : ControllerBase
{
    private readonly IWeatherService _weatherService;
    private readonly ILogger<WeatherForecastController> _logger;

    public WeatherForecastController(IWeatherService weatherService, ILogger<WeatherForecastController> logger)
    {
        _weatherService = weatherService ?? throw new ArgumentNullException(nameof(weatherService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 取得天氣預報清單
    /// </summary>
    /// <param name="count">要取得的天數，預設為5天</param>
    /// <returns>天氣預報清單</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<WeatherForecast>>> GetWeatherForecasts([FromQuery] int count = 5)
    {
        try
        {
            _logger.LogInformation("Getting weather forecasts for {Count} days", count);
            
            var forecasts = await _weatherService.GetWeatherForecastsAsync(count);
            return Ok(forecasts);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid argument: {Message}", ex.Message);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting weather forecasts");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// 根據日期取得天氣預報
    /// </summary>
    /// <param name="date">日期 (格式: yyyy-MM-dd)</param>
    /// <returns>指定日期的天氣預報</returns>
    [HttpGet("{date}")]
    public async Task<ActionResult<WeatherForecast>> GetWeatherForecastByDate(DateOnly date)
    {
        try
        {
            _logger.LogInformation("Getting weather forecast for date {Date}", date);
            
            var forecast = await _weatherService.GetWeatherForecastByDateAsync(date);
            
            if (forecast == null)
            {
                return NotFound($"Weather forecast for date {date} not found.");
            }

            return Ok(forecast);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid argument: {Message}", ex.Message);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting weather forecast for date {Date}", date);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// 建立新的天氣預報
    /// </summary>
    /// <param name="weatherForecast">天氣預報資料</param>
    /// <returns>建立的天氣預報</returns>
    [HttpPost]
    public async Task<ActionResult<WeatherForecast>> CreateWeatherForecast([FromBody] WeatherForecast weatherForecast)
    {
        try
        {
            _logger.LogInformation("Creating weather forecast for date {Date}", weatherForecast?.Date);
            
            var createdForecast = await _weatherService.CreateWeatherForecastAsync(weatherForecast!);
            
            return CreatedAtAction(
                nameof(GetWeatherForecastByDate), 
                new { date = createdForecast.Date }, 
                createdForecast);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid argument: {Message}", ex.Message);
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Invalid operation: {Message}", ex.Message);
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while creating weather forecast");
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// 更新天氣預報
    /// </summary>
    /// <param name="date">要更新的日期</param>
    /// <param name="weatherForecast">更新的天氣預報資料</param>
    /// <returns>更新後的天氣預報</returns>
    [HttpPut("{date}")]
    public async Task<ActionResult<WeatherForecast>> UpdateWeatherForecast(DateOnly date, [FromBody] WeatherForecast weatherForecast)
    {
        try
        {
            if (date != weatherForecast.Date)
            {
                return BadRequest("Date in URL does not match date in request body.");
            }

            _logger.LogInformation("Updating weather forecast for date {Date}", date);
            
            var updatedForecast = await _weatherService.UpdateWeatherForecastAsync(weatherForecast);
            
            if (updatedForecast == null)
            {
                return NotFound($"Weather forecast for date {date} not found.");
            }

            return Ok(updatedForecast);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid argument: {Message}", ex.Message);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while updating weather forecast for date {Date}", date);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }

    /// <summary>
    /// 刪除天氣預報
    /// </summary>
    /// <param name="date">要刪除的日期</param>
    /// <returns>刪除結果</returns>
    [HttpDelete("{date}")]
    public async Task<ActionResult> DeleteWeatherForecast(DateOnly date)
    {
        try
        {
            _logger.LogInformation("Deleting weather forecast for date {Date}", date);
            
            var deleted = await _weatherService.DeleteWeatherForecastAsync(date);
            
            if (!deleted)
            {
                return NotFound($"Weather forecast for date {date} not found.");
            }

            return NoContent();
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid argument: {Message}", ex.Message);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while deleting weather forecast for date {Date}", date);
            return StatusCode(500, "An error occurred while processing your request.");
        }
    }
}
