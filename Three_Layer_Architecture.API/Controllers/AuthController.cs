using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Three_Layer_Architecture.Repository.Models.DTOs;
using Three_Layer_Architecture.Service.Interfaces;

namespace Three_Layer_Architecture.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IAuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService ?? throw new ArgumentNullException(nameof(authService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 用戶登入
    /// </summary>
    /// <param name="request">登入請求</param>
    /// <returns>授權回應</returns>
    [HttpPost("login")]
    public async Task<ActionResult<AuthResponse>> Login([FromBody] LoginRequest request)
    {
        try
        {
            _logger.LogInformation("User login attempt for username: {Username}", request?.Username);

            var response = await _authService.LoginAsync(request!);
            if (response == null)
            {
                _logger.LogWarning("Login failed for username: {Username}", request?.Username);
                return Unauthorized(new { message = "使用者名稱或密碼錯誤" });
            }

            _logger.LogInformation("User login successful for username: {Username}", response.Username);
            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Login validation error: {Message}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during login");
            return StatusCode(500, new { message = "登入過程中發生錯誤" });
        }
    }

    /// <summary>
    /// 用戶註冊
    /// </summary>
    /// <param name="request">註冊請求</param>
    /// <returns>授權回應</returns>
    [HttpPost("register")]
    public async Task<ActionResult<AuthResponse>> Register([FromBody] RegisterRequest request)
    {
        try
        {
            _logger.LogInformation("User registration attempt for username: {Username}", request?.Username);

            var response = await _authService.RegisterAsync(request!);
            
            _logger.LogInformation("User registration successful for username: {Username}", response.Username);
            return CreatedAtAction(nameof(GetCurrentUser), null, response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Registration validation error: {Message}", ex.Message);
            return BadRequest(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Registration conflict: {Message}", ex.Message);
            return Conflict(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during registration");
            return StatusCode(500, new { message = "註冊過程中發生錯誤" });
        }
    }

    /// <summary>
    /// 取得目前登入用戶資訊
    /// </summary>
    /// <returns>用戶資訊</returns>
    [HttpGet("me")]
    [Authorize]
    public async Task<ActionResult<UserDto>> GetCurrentUser()
    {
        try
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
            {
                return Unauthorized(new { message = "無效的用戶身份" });
            }

            var user = await _authService.GetCurrentUserAsync(userId);
            if (user == null)
            {
                return NotFound(new { message = "用戶不存在" });
            }

            return Ok(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while getting current user");
            return StatusCode(500, new { message = "取得用戶資訊時發生錯誤" });
        }
    }

    /// <summary>
    /// 驗證 Token 是否有效
    /// </summary>
    /// <param name="token">JWT Token</param>
    /// <returns>驗證結果</returns>
    [HttpPost("validate")]
    public async Task<ActionResult<object>> ValidateToken([FromBody] string token)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(token))
            {
                return BadRequest(new { message = "Token 不能為空" });
            }

            var isValid = await _authService.ValidateTokenAsync(token);
            return Ok(new { isValid });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while validating token");
            return StatusCode(500, new { message = "驗證 Token 時發生錯誤" });
        }
    }

    /// <summary>
    /// 用戶登出（客戶端應該刪除 Token）
    /// </summary>
    /// <returns>登出結果</returns>
    [HttpPost("logout")]
    [Authorize]
    public ActionResult Logout()
    {
        // JWT Token 是無狀態的，實際的登出邏輯應該在客戶端實現（刪除 Token）
        // 這裡只是提供一個端點讓客戶端知道登出成功
        var username = User.FindFirst(ClaimTypes.Name)?.Value;
        _logger.LogInformation("User logout: {Username}", username);
        
        return Ok(new { message = "登出成功" });
    }
}
