### 授權功能測試檔案
### 使用 Visual Studio Code 的 REST Client 擴充功能執行這些請求

@baseUrl = https://localhost:7128
@contentType = application/json

### 1. 用戶註冊
POST {{baseUrl}}/api/auth/register
Content-Type: {{contentType}}

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "testpass123",
  "confirmPassword": "testpass123"
}

### 2. 用戶登入 (一般用戶)
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "username": "user",
  "password": "user123"
}

### 3. 管理員登入
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "username": "admin",
  "password": "admin123"
}

### 4. 取得目前用戶資訊 (需要 Token)
GET {{baseUrl}}/api/auth/me
Authorization: Bearer YOUR_TOKEN_HERE

### 5. 取得天氣預報 (需要授權)
GET {{baseUrl}}/api/weatherforecast
Authorization: Bearer YOUR_TOKEN_HERE

### 6. 建立天氣預報 (需要管理員權限)
POST {{baseUrl}}/api/weatherforecast
Content-Type: {{contentType}}
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE

{
  "date": "2024-12-25",
  "temperatureC": 25,
  "summary": "Christmas Day Weather"
}

### 7. 取得所有用戶 (需要管理員權限)
GET {{baseUrl}}/api/users
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE

### 8. 變更密碼
POST {{baseUrl}}/api/users/change-password
Content-Type: {{contentType}}
Authorization: Bearer YOUR_TOKEN_HERE

{
  "currentPassword": "user123",
  "newPassword": "newpass123"
}

### 9. 登出
POST {{baseUrl}}/api/auth/logout
Authorization: Bearer YOUR_TOKEN_HERE

### 10. 測試無授權存取 (應該回傳 401)
GET {{baseUrl}}/api/weatherforecast

### 11. 測試一般用戶存取管理員功能 (應該回傳 403)
GET {{baseUrl}}/api/users
Authorization: Bearer YOUR_USER_TOKEN_HERE
