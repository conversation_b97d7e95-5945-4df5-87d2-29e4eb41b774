using Three_Layer_Architecture.Repository.Interfaces;
using Three_Layer_Architecture.Repository.Repositories;
using Three_Layer_Architecture.Service.Interfaces;
using Three_Layer_Architecture.Service.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { 
        Title = "Three Layer Architecture API", 
        Version = "v1",
        Description = "A sample API demonstrating three-layer architecture pattern"
    });
});

// 註冊依賴注入服務
// Repository 層
builder.Services.AddScoped<IWeatherRepository, WeatherRepository>();

// Service 層
builder.Services.AddScoped<IWeatherService, WeatherService>();

// 加入 CORS 支援
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Three Layer Architecture API v1");
        c.RoutePrefix = string.Empty; // 讓 Swagger UI 在根路徑顯示
    });
}

app.UseHttpsRedirection();

app.UseCors("AllowAll");

app.UseAuthorization();

app.MapControllers();

app.Run();
